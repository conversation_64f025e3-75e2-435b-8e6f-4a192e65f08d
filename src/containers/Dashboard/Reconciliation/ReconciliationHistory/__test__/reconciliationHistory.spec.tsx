import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { describe, expect, it } from 'vitest';

import MockIndex from '+mock/MockIndex';

import ReconciliationHistory from '../index';

const MockedReconciliationHistory = () => {
  return (
    <MockIndex>
      <ReconciliationHistory />
    </MockIndex>
  );
};

describe('ReconciliationHistory', () => {
  it('should be accessible', async () => {
    const { container } = render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports between Kora and Processors.')).toBeInTheDocument();
    });

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should render the reconciliation history page with correct content', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports between Kora and Processors.')).toBeInTheDocument();
    });

    expect(
      screen.getByText(
        'This application enables you to check and match transaction details between Kora and Processors. It enables you identify and rectify errors or unpaid transactions, ensuring that the records of both parties are accurate.'
      )
    ).toBeInTheDocument();
  });

  it('should render the reconciliation history table with data', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    expect(screen.getByText('February 2024 Monnify Reconciliation')).toBeInTheDocument();
    expect(screen.getByText('March 2024 Flutterwave Reconciliation')).toBeInTheDocument();
    expect(screen.getByText('April 2024 Paystack Reconciliation')).toBeInTheDocument();
  });

  it('should display correct status indicators', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Successful')).toBeInTheDocument();
    });

    expect(screen.getByText('Processing')).toBeInTheDocument();
    expect(screen.getByText('Failed')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
  });

  it('should display formatted dates correctly', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('15 Jan 2024')).toBeInTheDocument();
    });

    expect(screen.getByText('10:00 AM')).toBeInTheDocument();
  });

  it('should show download action for completed reconciliations', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    const downloadActions = screen.getAllByTestId('action');
    expect(downloadActions).toHaveLength(1);
  });

  it('should handle table row interactions', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    const firstRow = screen.getByTestId('table-row-0-column-1');
    expect(firstRow).toBeInTheDocument();
    expect(firstRow).toHaveAttribute('role', 'button');
    expect(firstRow).toHaveAttribute('tabindex', '0');
  });

  it('should display table headers correctly', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Status')).toBeInTheDocument();
    });

    expect(screen.getByText('Reconciled Report')).toBeInTheDocument();
    expect(screen.getByText('Date Created')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('should show loading state while fetching data', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('Reconcile reports between Kora and Processors.')).toBeInTheDocument();
    });
  });

  it('should display pagination information and all table data correctly', async () => {
    render(<MockedReconciliationHistory />);

    await waitFor(() => {
      expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    });

    expect(screen.getByText('January 2024 Korapay Reconciliation')).toBeInTheDocument();
    expect(screen.getByText('Successful')).toBeInTheDocument();
    expect(screen.getByText('15 Jan 2024')).toBeInTheDocument();
    expect(screen.getByText('10:00 AM')).toBeInTheDocument();

    expect(screen.getByTestId('table_comp')).toBeInTheDocument();

    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Reconciled Report')).toBeInTheDocument();
    expect(screen.getByText('Date Created')).toBeInTheDocument();
    expect(screen.getByText('Action')).toBeInTheDocument();

    const downloadActions = screen.getAllByTestId('action');
    expect(downloadActions).toHaveLength(1);
  });
});
